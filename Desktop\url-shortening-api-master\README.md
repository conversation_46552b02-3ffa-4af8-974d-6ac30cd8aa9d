# Frontend Mentor - Shortly URL Shortening API Challenge Solution

This is a complete solution to the [Shortly URL shortening API Challenge on Frontend Mentor](https://www.frontendmentor.io/challenges/url-shortening-api-landing-page-2ce3ob-G). Frontend Mentor challenges help you improve your coding skills by building realistic projects.


## Table of contents

- [Overview](#overview)
  - [The challenge](#the-challenge)
  - [Features](#features)
- [My process](#my-process)
  - [Built with](#built-with)
  - [What I learned](#what-i-learned)
  - [Continued development](#continued-development)
  - [Useful resources](#useful-resources)
- [Getting Started](#getting-started)
  - [Installation](#installation)
  - [Usage](#usage)
- [API Integration](#api-integration)
- [Author](#author)

## Overview

### The challenge

Users should be able to:

- ✅ View the optimal layout for the site depending on their device's screen size
- ✅ Shorten any valid URL
- ✅ See a list of their shortened links, even after refreshing the browser
- ✅ Copy the shortened link to their clipboard in a single click
- ✅ Receive an error message when the `form` is submitted if:
  - The `input` field is empty
  - The URL format is invalid

![Design preview for the Shortly URL shortening API coding challenge](./design/desktop-preview.jpg)

### Features

**🎯 Core Functionality:**
- **URL Shortening**: Integrates with multiple APIs (CleanURI, TinyURL, is.gd) with fallback support
- **CORS Handling**: Uses proxy services to bypass browser restrictions
- **Form Validation**: Real-time validation with clear error messages
- **Local Storage**: Persistent storage of shortened links across sessions
- **Copy to Clipboard**: One-click copying with visual feedback

**📱 Responsive Design:**
- **Mobile-First**: Optimized for 375px mobile devices
- **Desktop Support**: Scales beautifully up to 1440px and beyond
- **Flexible Layout**: Uses CSS Flexbox and Grid for responsive layouts
- **Touch-Friendly**: Large tap targets and smooth interactions

**🎨 UI/UX Features:**
- **Mobile Navigation**: Hamburger menu with smooth animations
- **Loading States**: Visual feedback during API calls
- **Error Handling**: Graceful error messages and recovery
- **Smooth Animations**: CSS transitions and fade-in effects
- **Accessibility**: Semantic HTML and proper ARIA labels


## My process

### Built with

- **Semantic HTML5** markup with proper accessibility
- **CSS3** with custom properties (CSS variables)
- **Flexbox** and **CSS Grid** for layout
- **Mobile-first** responsive design workflow
- **Vanilla JavaScript** (ES6+) for functionality
- **Local Storage API** for data persistence
- **Fetch API** for HTTP requests
- **Multiple URL Shortening APIs** with fallback support

### What I learned

**🔧 Technical Skills Developed:**

1. **CORS Handling**: Learned to work around browser CORS restrictions using proxy services
2. **API Integration**: Implemented multiple API fallbacks for reliability
3. **Error Handling**: Built robust error handling with user-friendly messages
4. **Local Storage**: Implemented persistent data storage with JSON serialization
5. **Responsive Design**: Created truly responsive layouts that work on all devices

**💡 Key Code Implementations:**

```html
<!-- Semantic HTML structure -->
<section class="shorten" id="shorten">
  <form class="shorten__form" id="shorten-form">
    <div class="shorten__input-group">
      <input type="url" class="shorten__input" placeholder="Shorten a link here..." required>
      <span class="shorten__error">Please add a link</span>
    </div>
    <button type="submit" class="btn btn--primary">Shorten It!</button>
  </form>
</section>
```

```css
/* CSS Custom Properties for maintainable code */
:root {
  --primary-cyan: hsl(180, 66%, 49%);
  --primary-dark-violet: hsl(257, 27%, 26%);
  --container-max-width: 1110px;
}

/* Mobile-first responsive design */
.hero {
  flex-direction: column-reverse;
  text-align: center;
}

@media (min-width: 768px) {
  .hero {
    flex-direction: row;
    text-align: left;
  }
}
```

```javascript
// Multiple API fallback system
async function shortenUrl(originalUrl) {
  let shortenedUrl = null;

  // Try CleanURI with CORS proxy
  try {
    const response = await fetch(proxyUrl + cleanUriUrl, {
      method: 'POST',
      body: `url=${encodeURIComponent(urlToShorten)}`
    });
    // Handle response...
  } catch (error) {
    // Try TinyURL as fallback...
  }

  // Graceful fallback to demo mode if all APIs fail
  if (!shortenedUrl) {
    shortenedUrl = `https://short.ly/${generateShortCode()}`;
    showDemoNotice();
  }
}
```

### Continued development

**🚀 Future Enhancements:**
- **Backend Integration**: Implement a proper backend API to avoid CORS issues
- **User Accounts**: Add user registration and link management
- **Analytics**: Track click counts and usage statistics
- **Custom Domains**: Allow users to use custom short domains
- **QR Codes**: Generate QR codes for shortened URLs
- **Bulk Operations**: Support for shortening multiple URLs at once

### Useful resources

- [MDN Web Docs](https://developer.mozilla.org/) - Comprehensive web development documentation
- [CSS-Tricks](https://css-tricks.com/) - Excellent CSS techniques and best practices
- [Frontend Mentor](https://www.frontendmentor.io/) - Practice projects and community feedback
- [Can I Use](https://caniuse.com/) - Browser compatibility reference
- [AllOrigins](https://allorigins.win/) - CORS proxy service for API calls

## Getting Started

### Installation

1. **Clone or download** this repository to your local machine
2. **No build process required** - this is a vanilla HTML/CSS/JavaScript project
3. **Open `index.html`** in your web browser

### Usage

1. **Enter a URL** in the input field (e.g., `https://www.google.com` or `google.com`)
2. **Click "Shorten It!"** to generate a shortened URL
3. **Copy the link** by clicking the "Copy" button
4. **View your history** - all shortened links are saved and persist after refresh
5. **Responsive experience** - works seamlessly on mobile and desktop

## API Integration

**🔗 Multiple API Support:**

The application attempts to use real URL shortening services in this order:
1. **CleanURI API** (via CORS proxy)
2. **TinyURL API** (fallback)
3. **is.gd API** (second fallback)
4. **Demo Mode** (if all APIs fail due to CORS restrictions)

**🛡️ CORS Handling:**
- Uses `api.allorigins.win` as a CORS proxy
- Graceful degradation to demo mode when needed
- Clear user notification when in demo mode

## Author

**Frontend Mentor**: [@Ayokanmi-Adejola](https://www.frontendmentor.io/profile/Ayokanmi-Adejola)
